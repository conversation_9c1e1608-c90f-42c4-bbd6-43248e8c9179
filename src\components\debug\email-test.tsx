'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'

export function EmailTest() {
  const [testEmail, setTestEmail] = useState('')
  const [secret, setSecret] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const handleTest = async () => {
    if (!testEmail || !secret) {
      setError('Please provide both test email and secret')
      return
    }

    setIsLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch('/api/test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          testEmail,
          secret,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        setError(data.error || 'Test failed')
      } else {
        setResult(data)
      }
    } catch (err) {
      setError('Network error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Email Configuration Test</CardTitle>
        <CardDescription>
          Test the email delivery system to debug signup issues
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label htmlFor="testEmail" className="block text-sm font-medium mb-1">
            Test Email Address
          </label>
          <Input
            id="testEmail"
            type="email"
            placeholder="<EMAIL>"
            value={testEmail}
            onChange={(e) => setTestEmail(e.target.value)}
          />
        </div>
        
        <div>
          <label htmlFor="secret" className="block text-sm font-medium mb-1">
            Test Secret
          </label>
          <Input
            id="secret"
            type="password"
            placeholder="EMAIL_TEST_SECRET value"
            value={secret}
            onChange={(e) => setSecret(e.target.value)}
          />
        </div>

        <Button 
          onClick={handleTest} 
          disabled={isLoading || !testEmail || !secret}
          className="w-full"
        >
          {isLoading ? 'Sending Test Email...' : 'Send Test Email'}
        </Button>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {result && (
          <Alert>
            <AlertDescription>
              <div className="space-y-2">
                <p className="font-medium text-green-600">✅ Test email sent successfully!</p>
                <div className="text-sm text-gray-600">
                  <p><strong>Message ID:</strong> {result.details.messageId}</p>
                  <p><strong>Accepted:</strong> {result.details.accepted.join(', ')}</p>
                  {result.details.rejected.length > 0 && (
                    <p><strong>Rejected:</strong> {result.details.rejected.join(', ')}</p>
                  )}
                </div>
              </div>
            </AlertDescription>
          </Alert>
        )}

        <div className="text-xs text-gray-500 mt-4">
          <p><strong>Note:</strong> This tool requires the EMAIL_TEST_SECRET environment variable to be set.</p>
          <p>Check your email inbox (including spam folder) for the test message.</p>
        </div>
      </CardContent>
    </Card>
  )
}
