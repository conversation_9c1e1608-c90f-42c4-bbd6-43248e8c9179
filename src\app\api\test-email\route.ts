import { NextRequest, NextResponse } from 'next/server'
import nodemailer from 'nodemailer'

export async function POST(request: NextRequest) {
  try {
    // Check if email testing is enabled and has the correct secret
    const { testEmail, secret } = await request.json()
    
    if (!secret || secret !== process.env.EMAIL_TEST_SECRET) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    if (!testEmail) {
      return NextResponse.json(
        { error: 'Test email address is required' },
        { status: 400 }
      )
    }

    console.log(`🧪 Testing email configuration by sending to: ${testEmail}`)

    // Create transporter with the same configuration as the OTP emails
    const transporter = nodemailer.createTransport({
      host: process.env.SUPPORT_EMAIL_HOST,
      port: parseInt(process.env.SUPPORT_EMAIL_PORT || '465'),
      secure: true,
      auth: {
        user: process.env.SUPPORT_EMAIL,
        pass: process.env.SUPPORT_EMAIL_PASSWORD,
      },
    })

    // Test the connection first
    try {
      await transporter.verify()
      console.log('✅ SMTP connection verified successfully')
    } catch (verifyError) {
      console.error('❌ SMTP connection verification failed:', verifyError)
      return NextResponse.json(
        { 
          error: 'SMTP connection failed',
          details: verifyError instanceof Error ? verifyError.message : 'Unknown error'
        },
        { status: 500 }
      )
    }

    // Send test email
    const mailOptions = {
      from: `DocuChampAI Test <${process.env.SUPPORT_EMAIL}>`,
      to: testEmail,
      subject: 'Email Configuration Test - DocuChampAI',
      html: `
        <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #ffffff;">
          <div style="text-align: center; margin-bottom: 30px; padding: 20px 0; border-bottom: 1px solid #e5e7eb;">
            <h1 style="color: #7c3aed; margin: 0;">DocuChampAI</h1>
          </div>
          
          <div style="background-color: #f8fafc; padding: 30px; border-radius: 12px; margin: 20px 0;">
            <h2 style="color: #374151; margin-top: 0;">Email Configuration Test</h2>
            
            <p style="color: #6b7280; font-size: 16px; line-height: 1.6;">
              This is a test email to verify that the email configuration is working correctly.
            </p>
            
            <div style="background-color: #ffffff; border: 2px solid #10b981; border-radius: 8px; padding: 20px; margin: 25px 0; text-align: center;">
              <p style="color: #10b981; font-size: 18px; font-weight: 600; margin: 0;">
                ✅ Email delivery is working!
              </p>
            </div>
            
            <p style="color: #6b7280; font-size: 14px; line-height: 1.6;">
              <strong>Test Details:</strong><br>
              • Sent at: ${new Date().toISOString()}<br>
              • SMTP Host: ${process.env.SUPPORT_EMAIL_HOST}<br>
              • SMTP Port: ${process.env.SUPPORT_EMAIL_PORT}<br>
              • From: ${process.env.SUPPORT_EMAIL}
            </p>
          </div>
          
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 14px;">
            <p style="margin: 0;">
              This is an automated test email from DocuChampAI email system.
            </p>
          </div>
        </div>
      `,
    }

    const result = await transporter.sendMail(mailOptions)
    
    console.log('✅ Test email sent successfully:', {
      messageId: result.messageId,
      accepted: result.accepted,
      rejected: result.rejected,
      response: result.response
    })

    return NextResponse.json(
      {
        message: 'Test email sent successfully',
        details: {
          messageId: result.messageId,
          accepted: result.accepted,
          rejected: result.rejected,
          response: result.response
        }
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('❌ Email test failed:', error)
    return NextResponse.json(
      { 
        error: 'Email test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
