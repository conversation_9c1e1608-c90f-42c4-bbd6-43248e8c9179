import Stripe from 'stripe'
import { stripe } from './stripe'
import { prisma } from './prisma'
import { getCreditPackages } from './config'

// Basic Stripe utility functions
export async function createStripeCustomer(email: string, name?: string) {
  return stripe.customers.create({
    email,
    name,
  })
}

export async function createSubscriptionCheckout(
  customerId: string,
  priceId: string,
  successUrl: string,
  cancelUrl: string
) {
  return stripe.checkout.sessions.create({
    customer: customerId,
    payment_method_types: ['card'],
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: successUrl,
    cancel_url: cancelUrl,
    allow_promotion_codes: true,
  })
}

export async function createCustomerPortalSession(
  customerId: string,
  returnUrl: string
) {
  return stripe.billingPortal.sessions.create({
    customer: customerId,
    return_url: returnUrl,
  })
}

// Webhook handlers
export async function handleSubscriptionChange(subscription: Stripe.Subscription) {
  console.log('🔄 Processing subscription change:', subscription.id, 'Status:', subscription.status)

  const customerId = subscription.customer as string
  const subscriptionId = subscription.id
  const priceId = subscription.items.data[0]?.price.id

  // Find user by Stripe customer ID
  const user = await prisma.user.findUnique({
    where: { stripeCustomerId: customerId }
  })

  if (!user) {
    console.error('❌ User not found for Stripe customer:', customerId)
    return
  }

  console.log('👤 Found user:', user.email)

  // Get credit package info from config for this price ID
  const creditPackages = getCreditPackages()
  const creditPackage = creditPackages.find(pkg => pkg.stripePriceId === priceId)

  if (!creditPackage) {
    console.error('❌ Credit package not found for price ID:', priceId)
    console.log('Available packages:', creditPackages.map(p => ({ name: p.name, priceId: p.stripePriceId })))
    return
  }

  console.log('📦 Found credit package:', creditPackage.name, 'Credits:', creditPackage.credits)

  // Upsert subscription (no productId since we rely on Stripe as source of truth)
  try {
    await prisma.subscription.upsert({
      where: { stripeSubscriptionId: subscriptionId },
      update: {
        status: subscription.status,
        productName: creditPackage.name,
        currentPeriodStart: (subscription as any).current_period_start
          ? new Date((subscription as any).current_period_start * 1000)
          : new Date(),
        currentPeriodEnd: (subscription as any).current_period_end
          ? new Date((subscription as any).current_period_end * 1000)
          : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        cancelAtPeriodEnd: (subscription as any).cancel_at_period_end || false,
        updatedAt: new Date(),
      },
      create: {
        userId: user.id,
        stripeSubscriptionId: subscriptionId,
        stripeCustomerId: customerId,
        status: subscription.status,
        productName: creditPackage.name,
        currentPeriodStart: (subscription as any).current_period_start
          ? new Date((subscription as any).current_period_start * 1000)
          : new Date(),
        currentPeriodEnd: (subscription as any).current_period_end
          ? new Date((subscription as any).current_period_end * 1000)
          : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        cancelAtPeriodEnd: (subscription as any).cancel_at_period_end || false,
      },
    })

    console.log(`✅ Subscription ${subscription.status} for user ${user.email}`)
  } catch (error: any) {
    // Check if this is a unique constraint error for subscription ID
    if (error.code === 'P2002' && error.meta?.target?.includes('stripeSubscriptionId')) {
      console.log(`⚠️ Subscription ${subscriptionId} already exists, updating existing record`)

      // Try to update the existing subscription instead
      try {
        await prisma.subscription.update({
          where: { stripeSubscriptionId: subscriptionId },
          data: {
            status: subscription.status,
            productName: creditPackage.name,
            currentPeriodStart: (subscription as any).current_period_start
              ? new Date((subscription as any).current_period_start * 1000)
              : new Date(),
            currentPeriodEnd: (subscription as any).current_period_end
              ? new Date((subscription as any).current_period_end * 1000)
              : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            cancelAtPeriodEnd: (subscription as any).cancel_at_period_end || false,
            updatedAt: new Date(),
          },
        })
        console.log(`✅ Subscription ${subscription.status} updated for user ${user.email}`)
      } catch (updateError) {
        console.error('Failed to update existing subscription:', updateError)
        throw updateError
      }
    } else {
      console.error('Database error in handleSubscriptionChange:', error)
      throw error
    }
  }
}

export async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  const dbSubscription = await prisma.subscription.findUnique({
    where: { stripeSubscriptionId: subscription.id },
    include: { user: true }
  })

  if (!dbSubscription) {
    console.error('❌ Subscription not found for deletion:', subscription.id)
    return
  }

  await prisma.subscription.update({
    where: { stripeSubscriptionId: subscription.id },
    data: {
      status: 'canceled',
      updatedAt: new Date(),
    },
  })

  // Create transaction record for cancellation
  await prisma.transaction.create({
    data: {
      userId: dbSubscription.userId,
      type: 'cancel_completed',
      amount: 0,
      currency: 'HKD',
      status: 'completed',
      description: `Subscription canceled: ${(dbSubscription as any).productName || 'Unknown Plan'}`,
      // Expanded metadata fields
      stripeSubscriptionId: subscription.id,
      canceledAt: new Date().toISOString(),
      reason: 'subscription_deleted',
      metadata: {
        stripeSubscriptionId: subscription.id,
        canceledAt: new Date().toISOString(),
        reason: 'subscription_deleted',
      },
    },
  })

  console.log(`✅ Subscription canceled: ${subscription.id} for user ${dbSubscription.user.email}`)
}

// New function to handle subscription cancellation at period end
export async function handleSubscriptionCancelScheduled(subscription: Stripe.Subscription) {
  const dbSubscription = await prisma.subscription.findUnique({
    where: { stripeSubscriptionId: subscription.id },
    include: { user: true }
  })

  if (!dbSubscription) {
    console.error('❌ Subscription not found for cancel scheduling:', subscription.id)
    return
  }

  await prisma.subscription.update({
    where: { stripeSubscriptionId: subscription.id },
    data: {
      cancelAtPeriodEnd: true,
      updatedAt: new Date(),
    },
  })

  // Create transaction record for scheduled cancellation
  await prisma.transaction.create({
    data: {
      userId: dbSubscription.userId,
      type: 'cancel_scheduled',
      amount: 0,
      currency: 'HKD',
      status: 'completed',
      description: `Subscription cancellation scheduled: ${(dbSubscription as any).productName || 'Unknown Plan'}`,
      // Expanded metadata fields
      stripeSubscriptionId: subscription.id,
      cancelAtPeriodEnd: true,
      periodEnd: (subscription as any).current_period_end ? new Date((subscription as any).current_period_end * 1000).toISOString() : null,
      reason: 'cancel_at_period_end',
      metadata: {
        stripeSubscriptionId: subscription.id,
        cancelAtPeriodEnd: true,
        periodEnd: (subscription as any).current_period_end ? new Date((subscription as any).current_period_end * 1000).toISOString() : null,
        reason: 'cancel_at_period_end',
      },
    },
  })

  console.log(`✅ Subscription cancellation scheduled: ${subscription.id} for user ${dbSubscription.user.email}`)
}

export async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  console.log('⌛ Processing payment succeeded:', invoice.id)

  // Extract subscription ID from the invoice
  let subscriptionId: string | null = null

  // Check direct subscription field (older format)
  if ((invoice as any).subscription) {
    subscriptionId = (invoice as any).subscription as string
  }
  // Check parent.subscription_details.subscription (newer format)
  else if ((invoice as any).parent?.subscription_details?.subscription) {
    subscriptionId = (invoice as any).parent.subscription_details.subscription as string
  }

  if (!subscriptionId) {
    console.log('⚠️ Payment not associated with subscription:', invoice.id)
    return
  }

  // Get the subscription from our database (should already exist from subscription events)
  const subscription = await prisma.subscription.findUnique({
    where: { stripeSubscriptionId: subscriptionId },
    include: { user: true }
  })

  if (!subscription) {
    console.error('❌ Subscription not found in database:', subscriptionId)
    console.log('⚠️ This might indicate subscription events were not processed first')
    return
  }

  // Get credit package info from stored productName
  const creditPackages = getCreditPackages()
  const creditPackage = creditPackages.find(pkg => pkg.name === subscription.productName)

  if (!creditPackage) {
    console.error('❌ Credit package not found for product name:', subscription.productName)
    console.log('Available packages:', creditPackages.map(p => ({ name: p.name, priceId: p.stripePriceId })))
    return
  }

  // Determine payment scenario based on billing reason and subscription history
  const billingReason = (invoice as any).billing_reason
  const isNewSubscription = billingReason === 'subscription_create'
  const isRenewal = billingReason === 'subscription_cycle'
  const isSubscriptionUpdate = billingReason === 'subscription_update'

  console.log(`📋 Payment scenario: ${billingReason} (new: ${isNewSubscription}, renewal: ${isRenewal}, update: ${isSubscriptionUpdate})`)

  // Handle different payment scenarios
  if (isNewSubscription) {
    await handleNewSubscription(subscription, creditPackage, invoice)
  } else if (isRenewal) {
    await handleSubscriptionRenewal(subscription, creditPackage, invoice)
  } else if (isSubscriptionUpdate) {
    await handleSubscriptionUpgradeDowngrade(subscription, creditPackage, invoice)
  } 
  console.log(`✅ Payment processed for user ${subscription.user.email} (${billingReason})`)
}

// Helper function for new subscription payments
async function handleNewSubscription(subscription: any, creditPackage: any, invoice: Stripe.Invoice) {
  console.log(`🆕 New subscription: Adding ${creditPackage.credits} credits`)

  // Add credits to user
  await prisma.user.update({
    where: { id: subscription.userId },
    data: { credits: { increment: creditPackage.credits } }
  })

  // Create transaction record
  await prisma.transaction.create({
    data: {
      userId: subscription.userId,
      type: 'subscription_create',
      amount: (invoice as any).amount_paid,
      currency: invoice.currency.toUpperCase(),
      status: 'completed',
      stripeInvoiceId: invoice.id,
      credits: creditPackage.credits,
      description: `New subscription: ${creditPackage.name}`,
      stripeSubscriptionId: subscription.stripeSubscriptionId,
      packageName: creditPackage.name,
      packageCredit: creditPackage.credits,
      billingReason: 'subscription_create',
      metadata: {
        stripeSubscriptionId: subscription.stripeSubscriptionId,
        packageName: creditPackage.name,
        packageCredit: creditPackage.credits,
        billingReason: 'subscription_create',
      },
    },
  })
}

// Helper function for subscription renewal payments
async function handleSubscriptionRenewal(subscription: any, creditPackage: any, invoice: Stripe.Invoice) {
  console.log(`🔄 Subscription renewal: Resetting credits to ${creditPackage.credits}`)

  // Reset user credits to the package amount (not increment)
  await prisma.user.update({
    where: { id: subscription.userId },
    data: { credits: creditPackage.credits }
  })

  // Update subscription period
  await prisma.subscription.update({
    where: { id: subscription.id },
    data: {
      currentPeriodStart: new Date((invoice as any).period_start * 1000),
      currentPeriodEnd: new Date((invoice as any).period_end * 1000),
    }
  })

  // Create transaction record
  await prisma.transaction.create({
    data: {
      userId: subscription.userId,
      type: 'credit_renewal',
      amount: (invoice as any).amount_paid,
      currency: invoice.currency.toUpperCase(),
      status: 'completed',
      stripeInvoiceId: invoice.id,
      credits: creditPackage.credits,
      description: `Credit renewal: ${creditPackage.name}`,
      stripeSubscriptionId: subscription.stripeSubscriptionId,
      packageName: creditPackage.name,
      packageCredit: creditPackage.credits,
      billingReason: 'subscription_cycle',
      metadata: {
        stripeSubscriptionId: subscription.stripeSubscriptionId,
        packageName: creditPackage.name,
        packageCredit: creditPackage.credits,
        billingReason: 'subscription_cycle',
      },
    },
  })
}

// Helper function for subscription upgrade/downgrade payments
async function handleSubscriptionUpgradeDowngrade(subscription: any, creditPackage: any, invoice: Stripe.Invoice) {
  console.log(`🔄 Subscription change: Processing upgrade/downgrade`)

  // Get current user credits
  const user = await prisma.user.findUnique({
    where: { id: subscription.userId }
  })

  if (!user) {
    console.error('❌ User not found for subscription change')
    return
  }

  // Get subscription details for billing period
  const dbSubscription = await prisma.subscription.findUnique({
    where: { stripeSubscriptionId: subscription.stripeSubscriptionId }
  })

  if (!dbSubscription) {
    console.error('❌ Subscription not found in database')
    return
  }

  // Get the current billing period from the subscription
  const currentPeriodStart = dbSubscription.currentPeriodStart
  const currentPeriodEnd = dbSubscription.currentPeriodEnd

  console.log(`🔍 Current billing period: ${currentPeriodStart} to ${currentPeriodEnd}`)

  // Strategy: Find the maximum package/credits the user has had in the current billing period
  // This prevents users from gaming the system by downgrading and upgrading repeatedly

  let maxCreditsInPeriod = 0
  let maxPackageInPeriod = 'Unknown'
  let previousPackageName = 'Unknown'
  let previousPackageCredit = 0

  // Get ALL transactions for this user's subscription in the current billing period
  // We need to find the maximum package they've had, not just the most recent
  // Add 1-minute buffer to period start to catch transactions that happened slightly before subscription creation
  const periodStartWithBuffer = currentPeriodStart ?
    new Date(currentPeriodStart.getTime() - 60 * 1000) : // 1 minute before
    undefined

  console.log(`🕐 Period start with buffer: ${periodStartWithBuffer} (original: ${currentPeriodStart})`)

  const allTransactions = await prisma.transaction.findMany({
    where: {
      userId: subscription.userId,
      type: { in: ['subscription_create', 'upgrade', 'downgrade', 'credit_renewal'] },
      // Look in current billing period if available, otherwise look at all transactions
      ...(periodStartWithBuffer && currentPeriodEnd ? {
        createdAt: { gte: periodStartWithBuffer, lte: currentPeriodEnd }
      } : {})
    },
    orderBy: { createdAt: 'desc' }
  })

  console.log(`📋 Found ${allTransactions.length} transactions in current billing period`)

  // Find the maximum credits across ALL transactions in this period
  for (const transaction of allTransactions) {
    const transactionCredits = (transaction.metadata as any)?.packageCredit || 0
    const transactionPackage = (transaction.metadata as any)?.packageName || ''

    console.log(`📦 Transaction: ${transaction.type} - ${transactionPackage} (${transactionCredits} credits) at ${transaction.createdAt}`)

    if (transactionCredits > maxCreditsInPeriod) {
      maxCreditsInPeriod = transactionCredits
      maxPackageInPeriod = transactionPackage
      console.log(`🔝 New max found: ${maxPackageInPeriod} (${maxCreditsInPeriod} credits)`)
    }
  }

  // Get the most recent transaction for previous package info
  if (allTransactions.length > 0) {
    const lastTransaction = allTransactions[0]
    previousPackageCredit = (lastTransaction.metadata as any)?.packageCredit || 0
    previousPackageName = (lastTransaction.metadata as any)?.packageName || 'Unknown'
  }

  // If no transactions found in current period, look for the most recent transaction before this period
  if (maxCreditsInPeriod === 0 && periodStartWithBuffer) {
    console.log('⚠️ No transactions in current period, looking for baseline before period start (with buffer)')

    const baselineTransaction = await prisma.transaction.findFirst({
      where: {
        userId: subscription.userId,
        type: { in: ['subscription_create', 'upgrade', 'downgrade', 'credit_renewal'] },
        createdAt: { lt: periodStartWithBuffer }
      },
      orderBy: { createdAt: 'desc' }
    })

    if (baselineTransaction) {
      maxCreditsInPeriod = (baselineTransaction.metadata as any)?.packageCredit || 0
      maxPackageInPeriod = (baselineTransaction.metadata as any)?.packageName || 'Unknown'
      previousPackageCredit = maxCreditsInPeriod
      previousPackageName = maxPackageInPeriod
      console.log(`📦 Found baseline: ${maxPackageInPeriod} (${maxCreditsInPeriod} credits)`)
    }
  }

  console.log(`📊 Max in current period: ${maxPackageInPeriod} (${maxCreditsInPeriod} credits)`)
  console.log(`📊 Previous package: ${previousPackageName} (${previousPackageCredit} credits)`)

  // Calculate credit difference based on max credits in period, not previous package
  const creditDifference = creditPackage.credits - maxCreditsInPeriod

  // Determine if this is an upgrade or downgrade based on creditDifference
  const isUpgrade = creditDifference > 0
  const isDowngrade = creditDifference < 0
  const transactionType = isUpgrade ? 'upgrade' : isDowngrade ? 'downgrade' : 'subscription_update'

  console.log(`📊 ${isUpgrade ? 'Upgrade' : isDowngrade ? 'Downgrade' : 'Change'} from ${previousPackageName} (${previousPackageCredit}) to ${creditPackage.name} (${creditPackage.credits}), difference: ${creditDifference}`)

  let actualCreditChange = 0

  if (isDowngrade) {
    // For downgrades: Do NOT deduct credits immediately as user already paid for original plan
    // They should enjoy the original plan credits until period end
    console.log(`⏳ Downgrade detected: Credits will NOT be deducted immediately. User keeps current credits until period end.`)
    actualCreditChange = 0 // No immediate credit change
  } else if (isUpgrade) {
    // For upgrades: Only give additional credits if the new plan is higher than max they've had in this period
    console.log(`🔍 Upgrade validation: Current plan credits: ${creditPackage.credits}, Max credits had in period: ${maxCreditsInPeriod}`)

    if (creditPackage.credits > maxCreditsInPeriod) {
      // User is genuinely upgrading to a higher plan than they've had in this period
      actualCreditChange = creditPackage.credits - maxCreditsInPeriod
      console.log(`✅ Valid upgrade: User had max ${maxCreditsInPeriod} credits, giving ${actualCreditChange} additional credits`)
    } else {
      // User is not really upgrading (maybe downgraded then upgrading back)
      actualCreditChange = 0
      console.log(`⚠️ Not a genuine upgrade: User already had ${maxCreditsInPeriod} credits in this period. No credits added.`)
    }
  }

  // Apply credit changes only if needed
  if (actualCreditChange !== 0) {
    await prisma.user.update({
      where: { id: subscription.userId },
      data: { credits: { increment: actualCreditChange } }
    })
    console.log(`💳 Applied ${actualCreditChange} credit change to user`)
  }

  // Create transaction record
  const newTransaction = await prisma.transaction.create({
    data: {
      userId: subscription.userId,
      type: transactionType,
      amount: (invoice as any).amount_paid,
      currency: invoice.currency.toUpperCase(),
      status: 'completed',
      stripeInvoiceId: invoice.id,
      credits: actualCreditChange, // Use actual credit change, not the difference
      description: `Subscription ${transactionType}: ${creditPackage.name}${actualCreditChange === 0 ? ' (no immediate credit change)' : ''}`,
      metadata: {
        stripeSubscriptionId: subscription.stripeSubscriptionId,
        packageName: creditPackage.name,
        packageCredit: creditPackage.credits,
        previousPackage: previousPackageName,
        previousPackageCredit: previousPackageCredit,
        creditDifference: creditDifference,
        actualCreditChange: actualCreditChange,
        billingReason: 'subscription_update',
        // New fields for max package tracking
        maxPackageInPeriod: maxPackageInPeriod,
        maxCreditsInPeriod: maxCreditsInPeriod,
        upgradeValidation: isUpgrade ? {
          maxCreditsInPeriod: maxCreditsInPeriod,
          currentPlanCredits: creditPackage.credits,
          validUpgrade: actualCreditChange > 0,
          creditDifferenceCalculation: `${creditPackage.credits} - ${maxCreditsInPeriod} = ${actualCreditChange}`
        } : null,
        downgradeNote: isDowngrade ? 'Credits preserved until period end' : null,
        // Mark for success notification if it's a valid upgrade
        showSuccessModal: isUpgrade && actualCreditChange > 0,
        notificationPending: isUpgrade && actualCreditChange > 0,
      },
    },
  })

  console.log(`✅ Transaction recorded: ${transactionType} with ${actualCreditChange} credit change`)

  // Log if this is a valid upgrade that should show success modal
  if (isUpgrade && actualCreditChange > 0) {
    console.log(`🔔 Transaction ${newTransaction.id} marked for success notification`)
  }
}


// Helper function to process downgrade credit adjustments at period end
export async function processDowngradeAtPeriodEnd(subscriptionId: string) {
  console.log('🔄 Processing downgrade credit adjustment at period end for subscription:', subscriptionId)

  const subscription = await prisma.subscription.findUnique({
    where: { stripeSubscriptionId: subscriptionId },
    include: { user: true }
  })

  if (!subscription) {
    console.error('❌ Subscription not found:', subscriptionId)
    return
  }

  // Find the most recent downgrade transaction for this subscription
  const lastDowngrade = await prisma.transaction.findFirst({
    where: {
      userId: subscription.userId,
      type: 'downgrade',
      createdAt: {
        gte: subscription.currentPeriodStart || new Date(0),
        lte: subscription.currentPeriodEnd || new Date()
      }
    },
    orderBy: { createdAt: 'desc' }
  })

  if (!lastDowngrade) {
    console.log('ℹ️ No downgrade found in current period')
    return
  }

  const metadata = lastDowngrade.metadata as any
  const newPlanCredits = metadata?.packageCredit || 0

  if (newPlanCredits > 0 && subscription.user.credits > newPlanCredits) {
    const creditReduction = subscription.user.credits - newPlanCredits

    // Adjust user credits to match the downgraded plan
    await prisma.user.update({
      where: { id: subscription.userId },
      data: { credits: newPlanCredits }
    })

    // Record the credit adjustment
    await prisma.transaction.create({
      data: {
        userId: subscription.userId,
        type: 'downgrade_adjustment',
        amount: 0,
        currency: 'HKD',
        status: 'completed',
        credits: -creditReduction,
        description: `Period-end credit adjustment for downgrade to ${metadata?.packageName || 'Unknown Plan'}`,
        metadata: {
          originalCredits: subscription.user.credits,
          newCredits: newPlanCredits,
          creditReduction: creditReduction,
          downgradeTransactionId: lastDowngrade.id,
          adjustmentReason: 'period_end_downgrade',
        },
      },
    })

    console.log(`✅ Adjusted credits from ${subscription.user.credits} to ${newPlanCredits} (reduction: ${creditReduction})`)
  } else {
    console.log('ℹ️ No credit adjustment needed')
  }
}

export async function handlePaymentFailed(invoice: Stripe.Invoice) {
  if ((invoice as any).subscription) {
    const subscriptionId = (invoice as any).subscription as string

    // Get subscription to find user
    const subscription = await prisma.subscription.findUnique({
      where: { stripeSubscriptionId: subscriptionId },
      include: { user: true }
    })

    if (!subscription) {
      console.error('Subscription not found for failed payment:', subscriptionId)
      return
    }

    // Create failed transaction record
    await prisma.transaction.create({
      data: {
        userId: subscription.userId,
        type: 'payment_failed',
        amount: (invoice as any).amount_due,
        currency: invoice.currency.toUpperCase(),
        status: 'failed',
        stripeInvoiceId: invoice.id,
        description: `Payment failed for subscription`,
        metadata: {
          stripeSubscriptionId: subscriptionId,
          failureReason: 'payment_failed',
        },
      },
    })

    console.log(`❌ Payment failed for user ${subscription.user.email}`)
  }
}
